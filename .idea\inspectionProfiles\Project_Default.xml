<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AssignmentUsedAsCondition" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="BadExceptionCaught" enabled="false" level="INFORMATION" enabled_by_default="false" editorAttributes="INFORMATION_ATTRIBUTES">
      <option name="exceptionsString" value="" />
      <option name="exceptions">
        <value />
      </option>
    </inspection_tool>
    <inspection_tool class="CStyleArrayDeclaration" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="ControlFlowStatementWithoutBraces" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="CustomRegExpInspection" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="DefaultNotLastCaseInSwitch" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="EmptyFinallyBlock" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="EmptyStatementBody" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_reportEmptyBlocks" value="true" />
    </inspection_tool>
    <inspection_tool class="EmptyTryBlock" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="FallthruInSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="FieldNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <extension name="ConstantNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Z_\d]*" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="ConstantWithMutableFieldTypeNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Z_\d]*" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="EnumeratedConstantNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Z_\d]*" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="InstanceVariableNamingConvention" enabled="true">
        <option name="m_regex" value="[a-z][A-Za-z\d]*" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="StaticVariableNamingConvention" enabled="true">
        <option name="m_regex" value="[a-z][A-Za-z\d]*" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="255" />
      </extension>
    </inspection_tool>
    <inspection_tool class="IncorrectFormatting" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="LambdaParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_maxLength" value="255" />
    </inspection_tool>
    <inspection_tool class="LocalVariableHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethods" value="true" />
    </inspection_tool>
    <inspection_tool class="LocalVariableNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_ignoreForLoopParameters" value="false" />
      <option name="m_ignoreCatchParameters" value="false" />
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="255" />
    </inspection_tool>
    <inspection_tool class="LongLine" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="NestedAssignment" enabled="false" level="WARNING" enabled_by_default="false" editorAttributes="WARNING_ATTRIBUTES" />
    <inspection_tool class="NewClassNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <extension name="AbstractClassNamingConvention" enabled="true">
        <option name="inheritDefaultSettings" value="true" />
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="8" />
        <option name="m_maxLength" value="64" />
      </extension>
      <extension name="AnnotationNamingConvention" enabled="true">
        <option name="inheritDefaultSettings" value="true" />
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="8" />
        <option name="m_maxLength" value="64" />
      </extension>
      <extension name="ClassNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="2" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="EnumeratedClassNamingConvention" enabled="true">
        <option name="inheritDefaultSettings" value="true" />
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="8" />
        <option name="m_maxLength" value="64" />
      </extension>
      <extension name="InterfaceNamingConvention" enabled="true">
        <option name="inheritDefaultSettings" value="true" />
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="8" />
        <option name="m_maxLength" value="64" />
      </extension>
      <extension name="JUnitAbstractTestClassNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="2" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="JUnitTestClassNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="2" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="TestSuiteNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z][A-Za-z\d]*" />
        <option name="m_minLength" value="2" />
        <option name="m_maxLength" value="255" />
      </extension>
      <extension name="TypeParameterNamingConvention" enabled="true">
        <option name="m_regex" value="[A-Z]" />
        <option name="m_minLength" value="1" />
        <option name="m_maxLength" value="1" />
      </extension>
    </inspection_tool>
    <inspection_tool class="PackageNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_regex" value="[a-z_0-9]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="255" />
    </inspection_tool>
    <inspection_tool class="ParameterHidingMemberVariable" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_ignoreInvisibleFields" value="true" />
      <option name="m_ignoreStaticMethodParametersHidingInstanceFields" value="true" />
      <option name="m_ignoreForConstructors" value="true" />
      <option name="m_ignoreForPropertySetters" value="true" />
      <option name="m_ignoreForAbstractMethods" value="false" />
    </inspection_tool>
    <inspection_tool class="ParameterNamingConvention" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="m_regex" value="[a-z][A-Za-z\d]*" />
      <option name="m_minLength" value="1" />
      <option name="m_maxLength" value="255" />
    </inspection_tool>
    <inspection_tool class="PatternVariableHidesField" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="SSBasedInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <searchConfiguration name="First sentence should end with a period" description="The first sentence in a JavaDoc comment should end in a period" text="/**&#10; * $Comment$&#10; * @$Tag$ $TagValue$&#10; */" recursive="true" caseInsensitive="true" type="JAVA" pattern_context="default">
        <constraint name="__context__" within="" contains="" />
        <constraint name="Comment" script="&quot;var lines = Comment.text.split(&quot;\n|\r&quot;);&#10;boolean hasSentence = false;&#10;for (String line : lines) {&#10;    if (line.matches(&quot;.*[A-z0-9.].*&quot;)) {&#10;        if (line.matches(&quot;(\\s|\\*)*@.*&quot;)) {&#10;            return hasSentence&#10;        } else {&#10;            hasSentence = true&#10;        }&#10;        if (line.trim().contains(&quot;.&quot;)) return false;&#10;    }&#10;}&#10;return hasSentence;&quot;" target="true" within="" contains="" />
        <constraint name="Tag" minCount="0" maxCount="2147483647" within="" contains="" />
        <constraint name="TagValue" minCount="0" maxCount="2147483647" within="" contains="" />
      </searchConfiguration>
      <searchConfiguration name="Inner Assignment" description="Use of inner assignments can make code harder to read. Note: we do allow inner assignments in loops when they resemble a comparison." text="$Inst$ = $Expr$" recursive="true" caseInsensitive="true" type="JAVA" pattern_context="default">
        <constraint name="__context__" script="&quot;if (com.siyeh.ig.psiutils.ExpressionUtils.isVoidContext(__context__)) {&#10;    return false; &#10;}&#10;var element = com.intellij.psi.util.PsiUtil.skipParenthesizedExprUp(__context__.getParent());&#10;if (element instanceof com.intellij.psi.PsiBinaryExpression) {&#10;    var expression = (com.intellij.psi.PsiBinaryExpression)element;&#10;    &#10;    var tt =expression.getOperationTokenType(); &#10;    if (tt.equals(com.intellij.psi.JavaTokenType.NE)&#10;    || tt.equals(com.intellij.psi.JavaTokenType.EQEQ)&#10;    || tt.equals(com.intellij.psi.JavaTokenType.GE)&#10;    || tt.equals(com.intellij.psi.JavaTokenType.GT)&#10;    || tt.equals(com.intellij.psi.JavaTokenType.LE)&#10;    || tt.equals(com.intellij.psi.JavaTokenType.LT)) {&#10;        var enclosing = com.intellij.psi.util.PsiUtil.getEnclosingStatement(__context__);&#10;        if (enclosing instanceof com.intellij.psi.PsiConditionalLoopStatement) {&#10;            return false;&#10;        }&#10;    }&#10;}&#10;return true;&quot;" within="" contains="" />
        <constraint name="Inst" within="" contains="" />
        <constraint name="Expr" within="" contains="" />
      </searchConfiguration>
    </inspection_tool>
    <inspection_tool class="TooBroadCatch" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="onlyWarnOnRootExceptions" value="true" />
    </inspection_tool>
    <inspection_tool class="TooBroadThrows" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="onlyWarnOnRootExceptions" value="true" />
    </inspection_tool>
    <inspection_tool class="UNUSED_IMPORT" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem" />
    <inspection_tool class="UnnecessaryParentheses" enabled="true" level="WARNING" enabled_by_default="true" editorAttributes="Style Problem">
      <option name="ignoreClarifyingParentheses" value="true" />
      <option name="ignoreParenthesesOnConditionals" value="false" />
      <option name="ignoreParenthesesOnLambdaParameter" value="false" />
    </inspection_tool>
  </profile>
</component>